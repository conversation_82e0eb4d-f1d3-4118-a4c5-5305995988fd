import * as fs from "fs";
import pool from "../config/db.js";
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import { runQuery } from "#utils";
import { getTemplate } from "./template.service.js";
import { connectedUsers, io, notificationsNamespace } from "../../index.js";
import { noti_type } from "#middlewares/noti_type.js";
import moment from "moment-timezone";
import ct from "countries-and-timezones";
import countryCodes from "country-codes-list";
import { getCountNoti } from "./noti.service.js";
import { smsService } from "#services";

const generateId = () => {
  let d = new Date().getTime(),
    d2 =
      (typeof performance !== "undefined" &&
        performance.now &&
        performance.now() * 1000) ||
      0;
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    let r = Math.random() * 16;
    if (d > 0) {
      r = (d + r) % 16 | 0;
      d = Math.floor(d / 16);
    } else {
      r = (d2 + r) % 16 | 0;
      d2 = Math.floor(d2 / 16);
    }
    return (c == "x" ? r : (r & 0x7) | 0x8).toString(16);
  });
};
const notificationQuestionaire = async (qtn_id, lf_id) => {
  var con = await pool.getConnection();
  try {
    let sql_get_user_lf = `SELECT u.user_id, ui.country FROM users u JOIN user_info ui ON u.user_id = ui.user_id WHERE u.lf_id = ? AND u.role != 4`;
    let rst = await runQuery(con, sql_get_user_lf, [lf_id]);
    let lst_user_id = rst.reduce((acc, user) => {
      acc[user.user_id] = user.country;
      return acc;
    }, {});
    let connectedUser = Object.keys(connectedUsers).map((id) => parseInt(id));
    let lst_user_id_keys = Object.keys(lst_user_id).map((id) => parseInt(id));
    let commonUser = lst_user_id_keys.filter((id) =>
      connectedUser.map((id) => parseInt(id)).includes(id)
    );
    var message =
      "You are assigned a new questionnaire. Click to view details.";
    let sql = `INSERT INTO notification (message, created_at, status, user_id, source, type) VALUES (?, ?, 1, ?, ?, ?)`;
    // Promise.all(
    for (const [user_id, country] of Object.entries(lst_user_id)) {
      var time = getTimeInTimezone(convertCountryNameToISOCode(country));
      let result = await runQuery(con, sql, [
        message,
        time,
        user_id,
        qtn_id,
        noti_type.Lf_admin_assign_questionaire,
      ]);
      await con.commit();
      let count = await getCountNoti(user_id);
      if (connectedUsers[user_id]) {
        // commonUser.forEach((id) => {
        notificationsNamespace
          .to(connectedUsers[user_id]?.id)
          .emit("notification", {
            message: message,
            qtn_id: qtn_id,
            type: noti_type.Lf_admin_assign_questionaire,
            time: time,
            count: count[0]?.COUNT,
            status: 1,
            noti_id: result.insertId,
          });
        // });
        // );
      }
    }
  } catch (error) {
    await con.rollback();
    console.log(error);
  } finally {
    con.destroy();
  }
};
export const createQuestionaire = async (qtn_name, tem_id, lf_id, price) => {
  var con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let obj = await getTemplate(tem_id);
    if (obj.length == 0) {
      await con.commit();
      throw ErrorHandler.badRequestError("No template found");
    }
    let qtn_id = await createQuestionaireFromJson(
      obj,
      qtn_name,
      lf_id,
      price,
      tem_id
    );
    notificationQuestionaire(qtn_id, lf_id);
    return qtn_id;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getAllQuestionaire = async (
  page,
  size = 10,
  keyword,
  status,
  lf_id = null
) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    let questionaire, questionaireCount;
    if (keyword == null) {
      if (lf_id == null) {
        var sql = fs.readFileSync("app/sql/getAllQuestionaire.sql").toString();
        questionaire = await runQuery(con, sql, [
          status,
          (page - 1) * size,
          size * 1,
        ]);
        var sqlcount = fs
          .readFileSync("app/sql/getAllQuestionaireCount.sql")
          .toString();
        questionaireCount = await runQuery(con, sqlcount, [status]);
      } else {
        var sql = fs
          .readFileSync("app/sql/getAllQuestionaireLawfirm.sql")
          .toString();
        questionaire = await runQuery(con, sql, [
          lf_id,
          status,
          (page - 1) * size,
          size * 1,
        ]);
        var sqlcount = fs
          .readFileSync("app/sql/getAllQuestionaireLawfirmCount.sql")
          .toString();
        questionaireCount = await runQuery(con, sqlcount, [lf_id, status]);
      }
    } else {
      if (lf_id == null) {
        var sql = fs
          .readFileSync("app/sql/getAllQuestionaireKeyword.sql")
          .toString();
        questionaire = await runQuery(con, sql, [
          status,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          (page - 1) * size,
          size * 1,
        ]);
        var sqlcount = fs
          .readFileSync("app/sql/getAllQuestionaireCountKeyword.sql")
          .toString();
        questionaireCount = await runQuery(con, sqlcount, [
          status,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
        ]);
      } else {
        var sql = fs
          .readFileSync("app/sql/getAllQuestionaireLawfirmKeyword.sql")
          .toString();
        questionaire = await runQuery(con, sql, [
          lf_id,
          status,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          (page - 1) * size,
          size * 1,
        ]);
        var sqlcount = fs
          .readFileSync("app/sql/getAllQuestionaireLawfirmCountKeyword.sql")
          .toString();
        questionaireCount = await runQuery(con, sqlcount, [
          lf_id,
          status,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
        ]);
      }
    }
    let count = questionaireCount[0]["COUNT(*)"];
    await con.commit();
    return { questionaire, count };
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getQuestionaire = async (id) => {
  let con = await pool.getConnection();

  try {
    // Get all questionnaire data with optimized JOIN query
    var sql = fs.readFileSync("app/sql/getQuestionnaireWithData.sql").toString();
    let questionnaireData = await runQuery(con, sql, [id]);

    if (questionnaireData.length == 0) {
      return [];
    }

    // Get conditions separately
    var conditionsSql = fs.readFileSync("app/sql/getQuestionnaireConditions.sql").toString();
    let conditions = await runQuery(con, conditionsSql, [id]);

    // Process the flattened data into hierarchical structure
    let ans = {
      id: id,
      name: questionnaireData[0]?.qtn_name,
      description: questionnaireData[0]?.qtn_desc,
      status: questionnaireData[0]?.questionnaire_status,
      tem_id: questionnaireData[0]?.tem_id,
      lf_id: questionnaireData[0]?.lf_id,
      price: questionnaireData[0]?.questionnaire_price,
      lf_org_name: questionnaireData[0]?.lf_org_name,
      tem_name: questionnaireData[0]?.tem_name,
      groups: [],
      conditions: []
    };

    // Group the data by group_id, question_id while preserving order
    let groupsMap = new Map();
    let questionsMap = new Map();
    let groupOrder = []; // Track group order
    let questionOrder = new Map(); // Track question order per group

    for (const row of questionnaireData) {
      if (!row.group_id) continue;

      // Process group
      if (!groupsMap.has(row.group_id)) {
        groupsMap.set(row.group_id, {
          id: row.group_id,
          name: row.group_name,
          tooltips: row.group_tooltips,
          linkedTo: row.group_answer_id ? row.group_answer_id.split(", ") : null,
          questions: []
        });
        groupOrder.push(row.group_id); // Track order
        questionOrder.set(row.group_id, []); // Initialize question order for this group
      }

      // Process question
      if (row.question_id && !questionsMap.has(row.question_id)) {
        let question = {
          id: row.question_id,
          name: row.question_name,
          description: row.question_text,
          answer_type: row.answer_type,
          files: row.question_files ? JSON.parse(row.question_files) : null,
          required: row.required,
          tooltips: row.question_tooltips,
          selectAnswerTable: row.selectAnswerTable,
          linkedTo: row.parent_answer ? row.parent_answer.split(", ") : null,
          answers: {
            id: row.ans_id,
            name: row.answer_name,
            modal_id: row.modal_id,
            modal_type: row.modal_type,
            content: null
          }
        };

        // Handle answer content
        if (row.answer_content != null) {
          question.answers.content = row.answer_content;
        } else {
          // Collect all answers for this question
          let answersContent = [];
          for (const dataRow of questionnaireData) {
            if (dataRow.question_id === row.question_id && dataRow.answers_id) {
              answersContent.push({
                id: dataRow.answers_id,
                name: dataRow.answers_name,
                description: dataRow.answers_text
              });
            }
          }
          question.answers.content = answersContent.length > 0 ? answersContent : null;
        }

        questionsMap.set(row.question_id, question);
        questionOrder.get(row.group_id).push(row.question_id); // Track question order
      }
    }

    // Build groups array in the correct order
    ans.groups = [];
    for (const groupId of groupOrder) {
      let group = groupsMap.get(groupId);
      // Add questions in the correct order
      group.questions = [];
      for (const questionId of questionOrder.get(groupId)) {
        if (questionsMap.has(questionId)) {
          group.questions.push(questionsMap.get(questionId));
        }
      }
      ans.groups.push(group);
    }

    // Process conditions
    for (const index in conditions) {
      let temp = JSON.parse(conditions[index]?.content);
      ans.conditions[index] = temp;
    }

    // Get pending request
    ans.pending_request = await findRequest(id, 1);

    return ans;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

// Helper function to delete questionnaire using an existing connection
const deleteQuestionaireWithConnection = async (con, id) => {
  let groups = await runQuery(
    con,
    "Select * from `groups` where tem_id = ?",
    [id]
  );
  for (const index in groups) {
    let questions = await runQuery(
      con,
      "Select * from `questions` where gr_id = ?",
      [groups[index]?.id]
    );
    for (const j in questions) {
      let answer = await runQuery(
        con,
        "Select * from `answer` where ques_id = ?",
        [questions[j]?.id]
      );
      let answers = await runQuery(
        con,
        "Select * from `answers` where ques_id = ?",
        [answer[0]?.ans_id]
      );
      for (const k in answers) {
        await runQuery(
          con,
          "Update `answers` set ques_id = NULL where id = ?",
          [answers[k]?.id]
        );
      }
      await runQuery(
        con,
        "update `questions` set parent_answer = NULL ,gr_id = NULL where id = ?",
        [questions[j]?.id]
      );
      await runQuery(
        con,
        "update `answer` set ques_id = NULL  where id = ?",
        [answer[0]?.id]
      );
    }
    await runQuery(
      con,
      "update `groups` set answer_id = NULL, tem_id = NULL where id = ?",
      [groups[index]?.id]
    );
  }
  await runQuery(con, "Delete from `answer` where ques_id is NULL");
  await runQuery(con, "Delete from `answers` where ques_id is NULL");
  await runQuery(con, "Delete from `questions` where gr_id is NULL");
  await runQuery(con, "Delete from `groups` where tem_id is NULL");
  await runQuery(con, "Delete from `questionaires` where id = ?", id);
  await runQuery(con, "Delete from `conditions` where template_id = ?", id);
};

export const deleteQuestionaire = async (id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    await deleteQuestionaireWithConnection(con, id);
    await con.commit();
    return 1;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
function replaceQuestionIds(jsonData, id_list) {
  function traverse(data) {
    if (Array.isArray(data)) {
      for (let item of data) {
        traverse(item);
      }
    } else if (typeof data === "object") {
      if (data.type === "question" && data.questionId) {
        data.questionId = id_list[data.questionId];
        if (Array.isArray(data.value)) {
          let newAnswer = [];
          for (let answer of data.value) {
            newAnswer.push(id_list[answer]);
          }
          data.value = newAnswer;
        } else {
          if (id_list[data.value]) {
            data.value = id_list[data.value];
          }
        }
      }
      if (data.children) {
        for (let child of data.children) {
          traverse(child);
        }
      }
    }
  }

  traverse(jsonData.list);

  return jsonData;
}
export const updateQuestionaire = async (obj, lf_id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    // Delete the existing questionnaire (using the connection from this transaction)
    await deleteQuestionaireWithConnection(con, obj.id);

    // Create the new questionnaire from JSON (using the connection from this transaction)
    await updateQuestionaireFromJsonWithConnection(con, obj, lf_id);

    await con.commit();
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
//===================================================================
export const createQuestionaireFromJson = async (
  obj,
  qtn_name,
  lf_id,
  price,
  tem_id
) => {
  let con = await pool.getConnection();
  try {
    let id_list = [];
    await con.beginTransaction();
    let existingQtn = await runQuery(
      con,
      "SELECT id FROM questionaires WHERE qtn_name = ? AND lf_id = ?",
      [qtn_name, lf_id]
    );
    if (existingQtn.length > 0) {
      throw ErrorHandler.badRequestError("Questionnaire name already exists");
    }
    // Get template export config
    const templateResult = await runQuery(con,
      "SELECT export_config FROM templates WHERE id = ?",
      [tem_id]
    );
    
    let exportConfig = {
      pms_enabled: true,
      data_enabled: true,
      form_enabled: false,
      form_names: ''
    };

    if (templateResult.length > 0 && templateResult[0].export_config) {
      exportConfig = typeof templateResult[0].export_config === 'string'
        ? JSON.parse(templateResult[0].export_config)
        : templateResult[0].export_config;
    }

    // Update the SQL to include export_config
    var sql = fs.readFileSync("app/sql/createQuestionaireWithId.sql").toString();
    var qtn_id = generateId();
    id_list[obj.id] = qtn_id;
    obj.id = qtn_id;
    let qtn = await runQuery(con, sql, [
      qtn_id,
      qtn_name,
      obj.description,
      2,
      lf_id,
      typeof price !== "undefined" && price !== null ? price : obj.price,
      tem_id,
      JSON.stringify(exportConfig)
    ]);
    
    for (const group of obj?.groups) {
      var groupId = generateId();
      id_list[group.id] = groupId;
      group.id = groupId;
      var sql = fs.readFileSync("app/sql/createGroup.sql").toString();
      await runQuery(con, sql, [
        groupId,
        group.name,
        null,
        qtn_id,
        group.tooltips,
      ]);
      for (const question of group?.questions) {
        var questionId = generateId();
        var ansId = generateId();
        id_list[question.id] = questionId;
        id_list[question.answers.id] = ansId;
        question.id = questionId;
        question.answers.id = ansId;
        var sql = fs.readFileSync("app/sql/createQuestion.sql").toString();
        await runQuery(con, sql, [
          questionId,
          question.name,
          question.description,
          question.answer_type,
          null,
          null,
          question.required,
          question.selectAnswerTable,
          question.tooltips,
          JSON.stringify(question.files) ?? "",
        ]);
        if (question?.answers.content != null) {
          if (typeof question?.answers.content == "string") {
            await runQuery(
              con,
              "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`,`answer`) VALUES ( ?, ?, ?, ?, ?)",
              [
                ansId,
                question.answers.name,
                question.answer_type,
                null,
                question?.answers.content,
              ]
            );
          } else {
            await runQuery(
              con,
              "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`) VALUES ( ?, ?, ?, ?)",
              [ansId, question.answers.name, question.answer_type, null]
            );
            for (const answer of question?.answers.content) {
              var answerId = generateId();
              id_list[answer.id] = answerId;
              answer.id = answerId;
              var sql = fs.readFileSync("app/sql/createAnswer.sql").toString();
              await runQuery(con, sql, [
                answerId,
                answer.name,
                answer.description,
                null,
              ]);
            }
          }
        } else {
          await runQuery(
            con,
            "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`, `modal_id`,`modal_type`) VALUES ( ?, ?, ?, ?, ?, ?)",
            [
              ansId,
              question.answers.name,
              question.answer_type,
              null,
              question.answers.modal_id,
              question.answers.modal_type,
            ]
          );
        }
      }
    }
    for (const group of obj?.groups) {
      group?.linkedTo?.forEach((item, index) => {
        group.linkedTo[index] = id_list[item];
      });
      await runQuery(con, "update `groups` set answer_id = ? where id = ?", [
        typeof group?.linkedTo?.join(", ") !== "undefined"
          ? group?.linkedTo?.join(", ")
          : null,
        group.id,
      ]);
      for (const question of group?.questions) {
        question?.linkedTo?.forEach((item, index) => {
          question.linkedTo[index] = id_list[item];
        });
        await runQuery(
          con,
          "update `questions` set parent_answer = ?,gr_id = ? where id = ?",
          [
            typeof question?.linkedTo?.join(", ") !== "undefined"
              ? question?.linkedTo?.join(", ")
              : null,
            group.id,
            question.id,
          ]
        );
        await runQuery(
          con,
          "update `answer` set ques_id = ? where ans_id = ?",
          [question.id, question.answers.id]
        );
        if (question?.answers.content != null)
          if (typeof question?.answers.content != "string") {
            for (const answers of question?.answers.content) {
              await runQuery(
                con,
                "update `answers` set ques_id = ? where id = ?",
                [question.answers.id, answers.id]
              );
            }
          }
      }
    }
    for (let conditions of obj?.conditions) {
      let new_con = replaceQuestionIds(conditions, id_list);
      conditions.linkTo = id_list[conditions.linkTo];
      if (conditions?.repeatedBy)
        conditions.repeatedBy = id_list[conditions.repeatedBy];
      await runQuery(
        con,
        "INSERT INTO `conditions`(template_id,content) VALUES (?,?)",
        [qtn_id, JSON.stringify(new_con)]
      );
    }
    await con.commit();
    return qtn_id;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

const getAllReceiverActiveQuestionnaireMail = async (qtn_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs
      .readFileSync("app/sql/getAllReceiverActiveQuestionnaireMail.sql")
      .toString();
    let result = await runQuery(con, sql, [qtn_id]);
    await con.commit();
    return result;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

const getAllReceiverUpdateQuestionnaireMail = async (qtn_id, lf_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs
      .readFileSync("app/sql/getAllReceiverUpdateQuestionnaire.sql")
      .toString();
    let result = await runQuery(con, sql, [qtn_id, lf_id]);
    await con.commit();
    return result;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

const splitEmailsIntoChunks = (emails, chunkSize) => {
  const chunks = [];
  for (let i = 0; i < emails.length; i += chunkSize - 1) {
    const chunk = emails.slice(i, i + chunkSize - 1);
    if (chunk.length > 0) {
      chunks.push(chunk);
    }
  }
  return chunks;
};

export const updateQuestionaireStatus = async (obj, lf_id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    await runQuery(
      con,
      "Update `questionaires` set status = ? , update_at = Now() , update_by = ?  where id = ?",
      [obj.status, lf_id, obj.id]
    );
    if (obj.status == 1) {
      let receiver = await getAllReceiverActiveQuestionnaireMail(obj.id);
      for (let i = 0; i < receiver.length; i++) {
        smsService.sendMailActiveQuestionnaire(
          receiver[i].email,
          receiver[i].first_name,
          obj.id
        );
      }
      await runQuery(
        con,
        "Update `questionaires_request` set status = 0 where qtn_id = ?",
        [obj.id]
      );
    }
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

function extractQuestionIds(data) {
  let questionIds = [];
  function traverse(obj) {
    if (typeof obj === "object" && obj !== null) {
      if (obj.hasOwnProperty("questionId")) {
        questionIds.push(obj["questionId"]);
      }
      for (let key in obj) {
        traverse(obj[key]);
      }
    }
  }
  traverse(data);
  return questionIds;
}
function findUniqueGroups(questionIds, groupArray) {
  let uniqueGroups = new Set();
  for (let questionId of questionIds) {
    for (let group of groupArray) {
      for (let question of group.questions) {
        if (question.id === questionId) {
          uniqueGroups.add(group.id);
        }
      }
    }
  }
  return Array.from(uniqueGroups);
}
function mergeListsUnique(list1, list2) {
  let mergedSet = new Set([...list1, ...list2]);
  return Array.from(mergedSet);
}

function alterValueById(idToMatch, newValue, jsonData) {
  for (let group of jsonData.groups) {
    if (group.id === idToMatch) {
      if (newValue == null) return;
      if (group.linkedTo != null) {
        group.linkedTo = mergeListsUnique(group.linkedTo, newValue);
      } else group.linkedTo = newValue;
      return;
    }
    for (let question of group.questions) {
      if (question.id === idToMatch) {
        if (newValue == null) return;
        if (question.linkedTo != null) {
          question.linkedTo = mergeListsUnique(question.linkedTo, newValue);
        } else question.linkedTo = newValue;
      }
    }
  }
}
function findItemTypeById(idToFind, jsonData) {
  // Check if the ID matches any group IDs
  for (let group of jsonData.groups) {
    if (group.id === idToFind) {
      return "group"; // If matched, it's a group
    }
  }

  // If not a group, check if the ID matches any question IDs within groups
  for (let group of jsonData.groups) {
    for (let question of group.questions) {
      if (question.id === idToFind) {
        return "question"; // If matched, it's a question
      }
    }
  }

  // If not found, return null
  return null;
}
function removeElement(array, elem) {
  var index = array.indexOf(elem);
  if (index > -1) {
    array.splice(index, 1);
  }
}
function findQuestionsInGroup(groupId, questionIds, jsonData) {
  let group = jsonData.groups.find((group) => group.id === groupId);
  if (!group) {
    console.log("Group not found.");
    return [];
  }

  let groupQuestionIds = group.questions.map((question) => question.id);
  let questionsInGroup = questionIds.filter((questionId) =>
    groupQuestionIds.includes(questionId)
  );
  return questionsInGroup;
}

function findAnswerIdsByQuestionIds(questionIds, jsonData) {
  let answerIds = [];

  // Iterate through groups
  for (let group of jsonData.groups) {
    // Iterate through questions in the group
    for (let question of group.questions) {
      // Check if the question ID is in the list
      if (questionIds.includes(question.id)) {
        // If yes, collect answer IDs
        if (question.answers) {
          answerIds.push(question.answers.id);
        }
      }
    }
  }

  return answerIds;
}
export const createLink = async (obj) => {
  try {
    for (const groups of obj?.groups) {
      groups.linkedTo = null;
      for (const questions of groups?.questions) {
        questions.linkedTo = null;
      }
    }
    for (const conditions of obj?.conditions) {
      let temp = extractQuestionIds(conditions);
      // let final = findUniqueGroups(temp, obj?.groups);
      // let con = findItemTypeById(conditions?.linkTo, obj);
      // if (con == "question") {
      //   let group_question = findUniqueGroups(
      //     [conditions?.linkTo],
      //     obj?.groups
      //   );
      //   if (final.length == 1 && final[0] == group_question[0]) {
      let final = findAnswerIdsByQuestionIds(temp, obj);
      // } else {
      //   removeElement(final, group_question[0]);
      //   alterValueById(group_question[0], final, obj);
      //   let questions = findQuestionsInGroup(group_question[0], temp, obj);
      //   if (questions.length == 0) {
      //     final = null;
      //   } else {
      //     final = questions;
      //   }
      // }
      // }
      alterValueById(conditions.linkTo, final, obj);
    }
    return obj;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  }
};

// export const requestUpdateQuestionaire = async (obj, user_id, lf_id) => {
//   let con = await pool.getConnection();

//   try {
//     await con.beginTransaction();
//     await runQuery(
//       con,
//       "INSERT INTO questionaires_request(qtn_id,content,user_id,status,created_at) VALUES (?,?,?,1,NOW())",
//       [obj.qtn_id, obj.content, user_id]
//     );
//     await updateQuestionaireStatus({ id: obj.qtn_id, status: 3 }, lf_id);
//     //get admin login
//     let sql_get_admin = `SELECT u.user_id, ui.country FROM users u JOIN user_info ui ON u.user_id = ui.user_id WHERE role = ?`;
//     let rst = await runQuery(con, sql_get_admin, [1]);
//     let lst_user_id = rst.reduce((acc, user) => {
//       acc[user.user_id] = user.country;
//       return acc;
//     }, {});
//     // let connectedUser = Object.keys(connectedUsers);
//     // let lst_user_id_keys = Object.keys(lst_user_id).map((id) => parseInt(id));
//     // let commonUser = lst_user_id_keys.filter((id) =>
//     //   connectedUser.map((id) => parseInt(id)).includes(id)
//     // );
//     var message =
//       "You have a new request for changes to the questionaire. Click to view details.";
//     let sql = `INSERT INTO notification (message, created_at, status, user_id, source, type) VALUES (?, ?, 1, ?, ?, ?)`;
//     for (const [ad_user_id, country] of Object.entries(lst_user_id)) {
//       console.log(ad_user_id);
//       let time = getTimeInTimezone(convertCountryNameToISOCode(country));
//       await runQuery(con, sql, [
//         message,
//         time,
//         ad_user_id,
//         obj.qtn_id,
//         noti_type.Admin_Lawfirm_send_request,
//       ]);
//       await con.commit();
//       let count = await getCountNoti(ad_user_id);
//       if (connectedUsers[ad_user_id]) {
//         // commonUser.forEach((id) => {
//         notificationsNamespace
//           .to(connectedUsers[ad_user_id]?.id)
//           .emit("notification", {
//             message: message,
//             qtn_id: obj.qtn_id,
//             type: noti_type.Admin_Lawfirm_send_request,
//             time: time,
//             count: count[0]?.COUNT,
//             status: 1,
//           });
//         // });
//       }
//     }
//   } catch (error) {
//     console.log(error);
//     throw ErrorHandler.badRequestError(error.message);
//   } finally {
//     con.destroy();
//   }
// };
export const findRequest = async (qtn_id, status) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs.readFileSync("app/sql/findRequest.sql").toString();
    let request = await runQuery(con, sql, [qtn_id, status]);
    if (request.length > 0) {
      await con.commit();
      return request;
    } else {
      return [];
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
function hasQuestionIdInList(jsonData, targetQuestionId) {
  if (!jsonData.list || !Array.isArray(jsonData.list)) {
    return false;
  }

  for (let item of jsonData.list) {
    if (item.questionId === targetQuestionId) {
      return true;
    }
    // If the item is a group, recursively check its children
    if (item.type === "group" && item.children) {
      if (hasQuestionIdInList({ list: item.children }, targetQuestionId)) {
        return true;
      }
    }
  }

  return false;
}
export const modifyQuestion = async (obj) => {
  try {
    if (obj.change.questionId) {
      try {
        if (obj.change.mode == "delete") {
          let i = 0;
          while (i < obj?.conditions.length) {
            if (
              obj.conditions[i]?.linkTo == obj.change.questionId ||
              hasQuestionIdInList(obj.conditions[i], obj.change.questionId)
            ) {
              obj.conditions.splice(i, 1);
            } else {
              i += 1;
            }
          }
        } else {
          let i = 0;
          while (i < obj?.conditions.length) {
            if (hasQuestionIdInList(obj.conditions[i], obj.change.questionId)) {
              obj.conditions.splice(i, 1);
            } else {
              i += 1;
            }
          }
        }
        for (let group of obj.groups) {
          for (let question of group.questions) {
            if (question.id == obj.change.questionId) {
              if (obj.change.mode == "delete") {
                var index = group.questions.indexOf(question);
                if (index !== -1) {
                  group.questions.splice(index, 1);
                }
              } else {
                question.name = obj.change.name;
                question.description = obj.change.description;
                question.answer_type = obj.change.answer_type;
                question.answers = obj.change.answers;
                question.required = obj.change.required;
                question.selectAnswerTable = obj.change.selectAnswerTable;
                question.tooltips = obj.change.tooltips;
              }
            }
          }
        }
        delete obj.change;

        return await createLink(obj);
      } catch (error) {
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } else {
      try {
        for (let group of obj.groups) {
          if (group.id == obj.change.groupId) {
            for (let question of group.questions) {
              let i = 0;
              while (i < obj?.conditions.length) {
                if (
                  obj.conditions[i]?.linkTo == question.id ||
                  hasQuestionIdInList(obj.conditions[i], question.id)
                ) {
                  obj.conditions.splice(i, 1);
                } else {
                  i += 1;
                }
              }
            }
            let i = 0;
            while (i < obj?.conditions.length) {
              if (
                obj.conditions[i]?.linkTo == group.id ||
                hasQuestionIdInList(obj.conditions[i], group.id)
              ) {
                obj.conditions.splice(i, 1);
              } else {
                i += 1;
              }
            }
            var index = obj.groups.indexOf(group);
            if (index !== -1) {
              obj.groups.splice(index, 1);
            }
          }
        }
        delete obj.change;
        return await createLink(obj);
      } catch (error) {
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    }
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  }
};

// Helper function to update questionnaire from JSON using an existing connection
const updateQuestionaireFromJsonWithConnection = async (con, obj, email) => {
  let user = await runQuery(con, "Select user_id from users where email= ?", [
    email,
  ]);

  // Get current questionnaire export config
  const questionnaireResult = await runQuery(con,
    "SELECT export_config FROM questionaires WHERE id = ?",
    [obj.id]
  );

  let exportConfig = {
    pms_enabled: true,
    data_enabled: true,
    form_enabled: false,
    form_names: ''
  };

  if (questionnaireResult.length > 0 && questionnaireResult[0].export_config) {
    exportConfig = typeof questionnaireResult[0].export_config === 'string'
      ? JSON.parse(questionnaireResult[0].export_config)
      : questionnaireResult[0].export_config;
  }

  var sql = fs
    .readFileSync("app/sql/createQuestionaireWithId.sql")
    .toString();
  let qtn = await runQuery(con, sql, [
    obj.id,
    obj.name,
    obj.description,
    // obj.status,
    1,
    obj.lf_id,
    obj?.price || 0,
    obj.tem_id,
    JSON.stringify(exportConfig)
  ]);
  for (const group of obj?.groups) {
    var sql = fs.readFileSync("app/sql/createGroup.sql").toString();
    await runQuery(con, sql, [
      group.id,
      group.name,
      null,
      obj.id,
      group.tooltips,
    ]);
    for (const question of group?.questions) {
      var sql = fs.readFileSync("app/sql/createQuestion.sql").toString();
      await runQuery(con, sql, [
        question.id,
        question.name,
        question.description,
        question.answer_type,
        null,
        null,
        question.required,
        question.selectAnswerTable,
        question.tooltips,
        JSON.stringify(question.files) ?? "",
      ]);
      if (question?.answers.content != null) {
        if (typeof question?.answers.content == "string") {
          await runQuery(
            con,
            "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`,`answer`) VALUES ( ?, ?, ?, ?, ?)",
            [
              question.answers.id,
              question.answers.name,
              question.answer_type,
              null,
              question?.answers.content,
            ]
          );
        } else {
          await runQuery(
            con,
            "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`) VALUES ( ?, ?, ?, ?)",
            [
              question.answers.id,
              question.answers.name,
              question.answer_type,
              null,
            ]
          );
          for (const answer of question?.answers.content) {
            var sql = fs.readFileSync("app/sql/createAnswer.sql").toString();
            await runQuery(con, sql, [
              answer.id,
              answer.name,
              answer.description,
              null,
            ]);
          }
        }
      } else {
        await runQuery(
          con,
          "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`,`modal_id`,`modal_type`) VALUES ( ?, ?, ?, ?, ?, ?)",
          [
            question.answers.id,
            question.answers.name,
            question.answer_type,
            null,
            question.answers.modal_id,
            question.answers.modal_type,
          ]
        );
      }
    }
  }
  for (const group of obj?.groups) {
    await runQuery(con, "update `groups` set answer_id = ? where id = ?", [
      typeof group?.linkedTo?.join(", ") !== "undefined"
        ? group?.linkedTo?.join(", ")
        : null,
      group.id,
    ]);

    for (const question of group?.questions) {
      await runQuery(
        con,
        "update `questions` set parent_answer = ?,gr_id = ? where id = ?",
        [
          typeof question?.linkedTo?.join(", ") !== "undefined"
            ? question?.linkedTo?.join(", ")
            : null,
          group.id,
          question.id,
        ]
      );
      await runQuery(
        con,
        "update `answer` set ques_id = ? where ans_id = ?",
        [question.id, question.answers.id]
      );
      if (question?.answers.content != null)
        if (typeof question?.answers.content != "string") {
          for (const answers of question?.answers.content) {
            await runQuery(
              con,
              "update `answers` set ques_id = ? where id = ?",
              [question.answers.id, answers.id]
            );
          }
        }
    }
  }
  for (const conditions of obj?.conditions) {
    await runQuery(
      con,
      "INSERT INTO `conditions`(template_id,content) VALUES (?,?)",
      [obj?.id, JSON.stringify(conditions)]
    );
  }

  if (obj.status == 3) {
    let get_user_request = `SELECT user_id FROM questionaires_request WHERE qtn_id = ? AND status = 1`;
    var rst = await runQuery(con, get_user_request, [obj.id]);
    await runQuery(
      con,
      "Update `questionaires_request` set status = 0, update_at = NOW() where qtn_id = ?",
      [obj.id]
    );
    let receiver = await getAllReceiverUpdateQuestionnaireMail(
      obj?.id,
      obj?.lf_id
    );
    for (user of receiver) {
      smsService.sendMailUpdateQuestionnaire(
        user.email,
        user.first_name,
        obj.name,
        obj.id
      );
    }
    if (rst.length > 0) {
      var message =
        "Your questionaire change request has been approved. Click to view details.";
      let country = await getCountryByUserId(rst[0]?.user_id);
      let time = getTimeInTimezone(convertCountryNameToISOCode(country));
      let result = await runQuery(
        con,
        "INSERT INTO notification (message, created_at, status, user_id, source, type) VALUES (?, ?, 1, ?, ?, ?)",
        [
          message,
          time,
          rst[0]?.user_id,
          obj.id,
          noti_type.Lf_admin_edit_questionaire,
        ]
      );
      let count = await getCountNoti(rst[0]?.user_id);
      if (connectedUsers[rst[0]?.user_id]) {
        notificationsNamespace
          .to(connectedUsers[rst[0]?.user_id]?.id)
          .emit("notification", {
            message: message,
            qtn_id: obj.id,
            type: noti_type.Lf_admin_edit_questionaire,
            time: time,
            count: count[0]?.COUNT,
            status: 1,
            noti_id: result.insertId,
          });
      }
    }
  }
};

export const updateQuestionaireFromJson = async (obj, email) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    await updateQuestionaireFromJsonWithConnection(con, obj, email);
    await con.commit();
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
//==============================
export const getCountryByUserId = async (user_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs.readFileSync("app/sql/getCountryByUserId.sql").toString();
    let country = await runQuery(con, sql, [user_id]);
    if (country) {
      return country[0]?.country;
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getTimeInTimezone = (countryId) => {
  const timezones = getTimezone(countryId);

  return moment().tz(timezones[0]).format("YYYY-MM-DD HH:mm:ss");
};

export const getTimezone = (countryId) => {
  const timezones = ct.getTimezonesForCountry(countryId);
  if (timezones) {
    if (timezones.length > 0) {
      return timezones.map((tz) => tz.name);
    } else {
      return ["Timezone not found for this country"];
    }
  }
};

export const convertCountryNameToISOCode = (countryName) => {
  const countryData = Object.values(
    countryCodes.customList("countryNameEn", "{countryCode} {countryNameEn}")
  ).find((country) => country.includes(countryName));

  if (countryData) {
    // Extract the country code
    const [countryCode] = countryData.split(" ");
    return countryCode;
  } else {
    return "GB";
  }
};

export const getCountsForUsers = async (user_ids) => {
  let con = await pool.getConnection();
  try {
    let placeholders = user_ids.map(() => "?").join(",");
    let sql = `SELECT user_id, COUNT(*) AS COUNT FROM notification WHERE user_id IN (${placeholders}) AND status = 1 GROUP BY user_id`;
    let results = await runQuery(con, sql, user_ids);

    let counts = {};
    results.forEach((row) => {
      counts[row.user_id] = row.COUNT;
    });

    return counts;
  } catch (err) {
    console.log(err);
    return null;
  } finally {
    con.destroy();
  }
};

export const requestUpdateQuestionaire = async (obj, user_id, lf_id) => {
  const start = Date.now();
  const con = await pool.getConnection();

  try {
    await con.beginTransaction();

    await runQuery(
      con,
      "INSERT INTO questionaires_request(qtn_id,content,user_id,status,created_at) VALUES (?,?,?,1,NOW())",
      [obj.qtn_id, obj.content, user_id]
    );

    await updateQuestionaireStatus({ id: obj.qtn_id, status: 3 }, lf_id);
    let rst = await runQuery(
      con,
      "SELECT u.user_id, ui.country FROM users u JOIN user_info ui ON u.user_id = ui.user_id WHERE role = ?",
      [1]
    );

    let notifications = [];
    let message =
      "You have a new request for changes to the questionaire. Click to view details.";
    let ctemp_user = 0;
    for (const { user_id: ad_user_id, country } of rst) {
      let time = getTimeInTimezone(convertCountryNameToISOCode(country));
      notifications.push([
        message,
        time,
        1,
        ad_user_id,
        obj.qtn_id,
        noti_type.Admin_Lawfirm_send_request,
        ctemp_user,
      ]);
      ctemp_user++;
    }
    const query_noti = notifications.map((item) => item.slice(0, -1));
    if (notifications.length > 0) {
      let sql = `INSERT INTO notification (message, created_at, status, user_id, source, type) VALUES ?`;
      var result = await runQuery(con, sql, [query_noti]);
    }

    await con.commit();

    const user_ids = rst.map((user) => user.user_id);
    const counts = await getCountsForUsers(user_ids);

    for (const { user_id: ad_user_id } of rst) {
      if (connectedUsers[ad_user_id]) {
        const count = counts[ad_user_id];
        let time = notifications.find((n) => n[3] === ad_user_id)[1];
        let noti_id = notifications.find((n) => n[3] === ad_user_id)[6];
        notificationsNamespace
          .to(connectedUsers[ad_user_id]?.id)
          .emit("notification", {
            message,
            qtn_id: obj.qtn_id,
            type: noti_type.Admin_Lawfirm_send_request,
            time,
            count,
            status: 1,
            noti_id: result.insertId + noti_id,
          });
      }
    }
    const end = Date.now();
    console.log(end - start);
  } catch (error) {
    console.log(error);
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateQuestionaireExportConfig = async (qtnId, exportConfig) => {
  const con = await pool.getConnection();
  try {
    await con.beginTransaction();
    
    await runQuery(con, 
      "UPDATE questionaires SET export_config = ? WHERE id = ?",
      [JSON.stringify(exportConfig), qtnId]
    );
    
    await con.commit();
    return { success: true };
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getQuestionaireExportConfig = async (qtnId) => {
  const con = await pool.getConnection();
  try {
    const result = await runQuery(con,
      "SELECT export_config FROM questionaires WHERE id = ?",
      [qtnId]
    );
    
    if (result.length > 0 && result[0].export_config) {
      return typeof result[0].export_config === 'string'
        ? JSON.parse(result[0].export_config)
        : result[0].export_config;
    }
    
    return {
      pms_enabled: true,
      data_enabled: true,
      form_enabled: false,
      form_names: ''
    };
  } finally {
    con.destroy();
  }
};

